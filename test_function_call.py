#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 测试Function Call功能
验证LLM是否能正确调用检索工具
"""

import asyncio
import logging
import json
from model_interface import model_service
from schemas import RetrieveNERExamplesTool

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_function_call():
    """测试Function Call基本功能"""
    print("🧪 测试Function Call功能...")
    
    # 简单的测试prompt
    test_prompt = """Task: Retrieve NER examples for entity extraction.

Entity types: person, organization, location
Input text: "<PERSON> works at Apple Inc. in New York."

Call retrieve_ner_examples tool with:
- description: Brief description of needed examples (max 50 words)
- k: Number of examples (1-3)

MUST call retrieve_ner_examples tool now."""

    tools = [RetrieveNERExamplesTool]
    messages = [{"role": "user", "content": test_prompt}]
    
    try:
        print("📤 发送Function Call请求...")
        response = await model_service.generate_with_tools_async(
            messages=messages,
            tools=tools
        )
        
        if response:
            print(f"✅ 收到响应: {type(response)}")
            print(f"📝 响应内容: {response.content if hasattr(response, 'content') else 'No content'}")
            
            if hasattr(response, 'tool_calls') and response.tool_calls:
                print(f"🔧 工具调用数量: {len(response.tool_calls)}")
                for i, tool_call in enumerate(response.tool_calls):
                    print(f"  工具{i+1}: {tool_call.function.name}")
                    print(f"  参数: {tool_call.function.arguments}")
                    
                    # 尝试解析参数
                    try:
                        args = json.loads(tool_call.function.arguments)
                        print(f"  解析成功: {args}")
                    except json.JSONDecodeError as e:
                        print(f"  ❌ 参数解析失败: {e}")
                        print(f"  原始参数: {tool_call.function.arguments}")
                        
                return True
            else:
                print("❌ 没有工具调用")
                return False
        else:
            print("❌ 没有收到响应")
            return False
            
    except Exception as e:
        print(f"❌ Function Call测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_schema_generation():
    """测试Schema生成"""
    print("\n🔍 测试Schema生成...")
    
    from model_interface import pydantic_to_openai_tool
    
    try:
        schema = pydantic_to_openai_tool(RetrieveNERExamplesTool)
        print("✅ Schema生成成功:")
        print(json.dumps(schema, indent=2, ensure_ascii=False))
        return True
    except Exception as e:
        print(f"❌ Schema生成失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始Function Call诊断测试\n")
    
    # 测试1: Schema生成
    schema_ok = await test_schema_generation()
    
    # 测试2: Function Call
    if schema_ok:
        call_ok = await test_function_call()
        
        if call_ok:
            print("\n✅ Function Call功能正常")
        else:
            print("\n❌ Function Call功能异常")
    else:
        print("\n❌ Schema生成异常，跳过Function Call测试")

if __name__ == "__main__":
    asyncio.run(main())
