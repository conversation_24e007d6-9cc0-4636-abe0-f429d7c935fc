#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧠 APIICL - 元认知智能体NER系统 (三阶段统一处理版本)
阶段1: 统一生成检索请求
阶段2: 统一检索
阶段3: 统一NER
"""

import asyncio
import argparse
import logging
import json

import os
from typing import Dict, Any, Optional
from datetime import datetime

from config import CONFIG, set_dataset, list_available_datasets, get_current_dataset_info, initialize_datasets
from schemas import RetrieveNERExamplesTool


def setup_logging(level: str = "WARNING"):
    """设置日志配置"""
    import sys
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S',
        stream=sys.stderr
    )


def print_progress_bar(stage_name: str, current: int, total: int, width: int = 50):
    """打印美观的进度条"""
    percentage = current / total if total > 0 else 0
    filled = int(width * percentage)
    bar = '█' * filled + '░' * (width - filled)
    print(f"\r{stage_name} [{bar}] {percentage:.1%}", end='', flush=True)
    if current == total:
        print()


def print_banner():
    """打印系统横幅"""
    print("=" * 60)
    print("🧠 APIICL - 元认知智能体NER系统 (三阶段统一处理)")
    print("📚 阶段1: 统一生成检索请求 → 阶段2: 统一检索 → 阶段3: 统一NER")
    print("=" * 60)


async def process_and_eval_dataset(max_samples: Optional[int] = None) -> Dict[str, Any]:
    """🎯 三阶段统一处理数据集"""

    # 获取当前数据集信息
    current_dataset = get_current_dataset_info()
    dataset_path = current_dataset['path']

    # 检查测试集文件
    test_path = dataset_path.replace('train.json', 'test.json')
    if not os.path.exists(test_path):
        print(f"❌ 测试集文件不存在: {test_path}")
        return {}

    # 阶段0：数据准备和向量库预初始化
    print("📚 阶段0：数据准备和向量库预初始化")
    try:
        with open(test_path, 'r', encoding='utf-8') as f:
            test_data = json.load(f)
    except Exception as e:
        print(f"❌ 加载测试集失败: {e}")
        return {}

    # 限制样本数量
    if max_samples and max_samples < len(test_data):
        test_data = test_data[:max_samples]

    # 预初始化向量库
    print("🔍 预初始化向量库...")
    from example_retriever import ExampleRetriever
    global_retriever = ExampleRetriever()
    vector_ready = await global_retriever.initialize_vector_store()
    if vector_ready:
        print("✅ 向量库预初始化成功")
    else:
        print("⚠️ 向量库预初始化失败，将使用直接模式")

    print_progress_bar("📚 数据准备阶段", 1, 1)
    
    # 初始化元认知智能体
    from meta_cognitive_agent import get_meta_cognitive_agent
    agent = get_meta_cognitive_agent(global_retriever)
    
    # {{ AURA-X: Add - 三阶段统一处理实现. Approval: 寸止(ID:1738230700). }}
    
    # {{ AURA-X: Add - 实现缓存和批量并发处理机制. Approval: 寸止(ID:1738230800). }}

    # 阶段1：统一生成检索请求 (并发+缓存+批量发送)
    print("🧠 阶段1：统一生成检索请求 (并发+缓存+批量发送)")

    # 检查缓存
    cache_dir = "./cache/requests"
    os.makedirs(cache_dir, exist_ok=True)
    cache_file = os.path.join(cache_dir, f"requests_{current_dataset['name'].lower().replace(' ', '_')}_{len(test_data)}.json")

    if os.path.exists(cache_file):
        print("📦 发现检索请求缓存，正在加载...")
        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                all_retrieval_requests = json.load(f)
            print(f"✅ 从缓存加载了 {len(all_retrieval_requests)} 个检索请求")
        except:
            print("⚠️ 缓存文件损坏，重新生成...")
            all_retrieval_requests = None
    else:
        all_retrieval_requests = None

    if all_retrieval_requests is None:
        async def generate_single_request(i, sample):
            """并发生成单个检索请求"""
            text = sample.get('text', '')

            try:
                # 生成检索请求
                stage1_prompt = agent._build_stage1_prompt(text)
                tools = [RetrieveNERExamplesTool]
                messages = [{"role": "user", "content": stage1_prompt}]

                response = await agent.model_service.generate_with_tools_async(
                    messages=messages,
                    tools=tools
                )

                if response and hasattr(response, 'tool_calls') and response.tool_calls:
                    for tool_call in response.tool_calls:
                        if tool_call.function.name == "RetrieveNERExamplesTool":
                            try:
                                arguments = json.loads(tool_call.function.arguments)
                                description = arguments.get("description", "")
                                k = arguments.get("k", 3)
                                return (i, description, k)
                            except:
                                return (i, "general NER examples", 3)

                return (i, "general NER examples", 3)

            except Exception as e:
                print(f"⚠️ 样本 {i} 生成检索请求失败: {e}")
                return (i, "general NER examples", 3)

        # {{ AURA-X: Modify - 批量并发处理，去掉延迟，直接发送. Approval: 寸止(ID:1738230900). }}
        batch_size = CONFIG.get('batch_size', 200)  # 使用更大的批处理大小
        all_retrieval_requests = []

        total_batches = (len(test_data) + batch_size - 1) // batch_size

        for i in range(0, len(test_data), batch_size):
            batch_samples = test_data[i:i+batch_size]
            batch_indices = list(range(i, min(i+batch_size, len(test_data))))
            batch_num = i//batch_size + 1

            print(f"🔄 阶段1批次 {batch_num}/{total_batches} ({len(batch_samples)}个样本)")

            # 创建批次任务
            batch_tasks = [
                generate_single_request(idx, sample)
                for idx, sample in zip(batch_indices, batch_samples)
            ]

            # 批量并发执行，一同等待
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

            # 处理结果
            for result in batch_results:
                if isinstance(result, Exception):
                    print(f"⚠️ 批次任务失败: {result}")
                else:
                    all_retrieval_requests.append(result)

            # {{ AURA-X: Add - 改进进度条显示. Approval: 寸止(ID:1738230900). }}
            print_progress_bar(f"🧠 阶段1进度 (批次{batch_num}/{total_batches})", len(all_retrieval_requests), len(test_data))

        # 按样本ID排序，保持顺序
        all_retrieval_requests.sort(key=lambda x: x[0])

        # 保存到缓存
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(all_retrieval_requests, f, ensure_ascii=False, indent=2)
            print(f"💾 检索请求已缓存到: {cache_file}")
        except Exception as e:
            print(f"⚠️ 缓存保存失败: {e}")
    
    print()
    
    # 阶段2：统一检索 (并发+缓存+批量发送)
    print("🔍 阶段2：统一检索 (并发+缓存+批量发送)")

    # 检查检索结果缓存
    examples_cache_file = os.path.join(cache_dir, f"examples_{current_dataset['name'].lower().replace(' ', '_')}_{len(test_data)}.json")

    if os.path.exists(examples_cache_file):
        print("📦 发现检索结果缓存，正在加载...")
        try:
            with open(examples_cache_file, 'r', encoding='utf-8') as f:
                all_examples = json.load(f)
            # 转换字符串键为整数键
            all_examples = {int(k): v for k, v in all_examples.items()}
            print(f"✅ 从缓存加载了 {len(all_examples)} 个检索结果")
        except:
            print("⚠️ 检索缓存文件损坏，重新检索...")
            all_examples = None
    else:
        all_examples = None

    if all_examples is None:
        async def retrieve_single_example(sample_id, description, k):
            """并发检索单个示例"""
            try:
                examples = await agent._simple_retrieval(description, k)
                return (sample_id, examples)
            except Exception as e:
                print(f"⚠️ 样本 {sample_id} 检索失败: {e}")
                return (sample_id, [])

        all_examples = {}

        # 批量并发检索：分批发送，缓慢执行，一同等待
        for i in range(0, len(all_retrieval_requests), batch_size):
            batch_requests = all_retrieval_requests[i:i+batch_size]

            print(f"🔄 检索批次 {i//batch_size + 1}/{(len(all_retrieval_requests) + batch_size - 1)//batch_size}")

            # 创建批次任务
            batch_tasks = [
                retrieve_single_example(sample_id, description, k)
                for sample_id, description, k in batch_requests
            ]

            # 批量并发执行，一同等待
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

            # 处理结果
            for result in batch_results:
                if isinstance(result, Exception):
                    print(f"⚠️ 检索任务失败: {result}")
                else:
                    sample_id, examples = result
                    all_examples[sample_id] = examples

            # {{ AURA-X: Remove - 去掉批次间不必要的延迟. Approval: 寸止(ID:1738230900). }}
            # 批次间不需要等待，直接发送下一批

            print_progress_bar("🔍 统一检索", len(all_examples), len(all_retrieval_requests))

        # 保存检索结果到缓存
        try:
            with open(examples_cache_file, 'w', encoding='utf-8') as f:
                json.dump(all_examples, f, ensure_ascii=False, indent=2)
            print(f"💾 检索结果已缓存到: {examples_cache_file}")
        except Exception as e:
            print(f"⚠️ 检索缓存保存失败: {e}")
    
    print()
    
    # 阶段3：统一NER (并发+缓存+批量发送)
    print("🎯 阶段3：统一NER (并发+缓存+批量发送)")

    # 检查NER结果缓存
    ner_cache_file = os.path.join(cache_dir, f"ner_results_{current_dataset['name'].lower().replace(' ', '_')}_{len(test_data)}.json")

    if os.path.exists(ner_cache_file):
        print("📦 发现NER结果缓存，正在加载...")
        try:
            with open(ner_cache_file, 'r', encoding='utf-8') as f:
                cached_results = json.load(f)
            print(f"✅ 从缓存加载了 {len(cached_results)} 个NER结果")
            results = cached_results
        except:
            print("⚠️ NER缓存文件损坏，重新执行NER...")
            results = None
    else:
        results = None

    if results is None:
        async def execute_single_ner(i, sample):
            """并发执行单个NER"""
            text = sample.get('text', '')
            true_labels = sample.get('label', {})
            examples = all_examples.get(i, [])

            try:
                # 执行NER
                predicted_labels = await agent._execute_ner_stage(text, examples)
            except Exception as e:
                print(f"⚠️ 样本 {i} NER失败: {e}")
                predicted_labels = {}

            if not isinstance(predicted_labels, dict):
                predicted_labels = {}

            # 计算指标
            sample_correct = 0
            sample_total = sum(len(entities) for entities in true_labels.values())
            sample_predicted = sum(len(entities) for entities in predicted_labels.values())

            for entity_type, true_entities in true_labels.items():
                predicted_entities_of_type = predicted_labels.get(entity_type, [])
                for entity in true_entities:
                    if entity in predicted_entities_of_type:
                        sample_correct += 1

            return {
                'text': text,
                'true_labels': true_labels,
                'predicted_labels': predicted_labels,
                'correct': sample_correct,
                'total_true': sample_total,
                'total_predicted': sample_predicted
            }

        results = []

        # 批量并发NER：分批发送，缓慢执行，一同等待
        for i in range(0, len(test_data), batch_size):
            batch_samples = test_data[i:i+batch_size]
            batch_indices = list(range(i, min(i+batch_size, len(test_data))))

            print(f"🔄 NER批次 {i//batch_size + 1}/{(len(test_data) + batch_size - 1)//batch_size}")

            # 创建批次任务
            batch_tasks = [
                execute_single_ner(idx, sample)
                for idx, sample in zip(batch_indices, batch_samples)
            ]

            # 批量并发执行，一同等待
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

            # 处理结果
            for result in batch_results:
                if isinstance(result, Exception):
                    print(f"⚠️ NER任务失败: {result}")
                else:
                    results.append(result)

            # {{ AURA-X: Remove - 去掉批次间不必要的延迟. Approval: 寸止(ID:1738230900). }}
            # 批次间不需要等待，直接发送下一批

            print_progress_bar("🎯 统一NER", len(results), len(test_data))

        # 保存NER结果到缓存
        try:
            with open(ner_cache_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"💾 NER结果已缓存到: {ner_cache_file}")
        except Exception as e:
            print(f"⚠️ NER缓存保存失败: {e}")

    # 计算汇总指标
    correct_predictions = sum(r['correct'] for r in results)
    total_entities = sum(r['total_true'] for r in results)
    predicted_entities = sum(r['total_predicted'] for r in results)
    
    print()

    # 计算最终指标
    precision = correct_predictions / predicted_entities if predicted_entities > 0 else 0
    recall = correct_predictions / total_entities if total_entities > 0 else 0
    f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    # 显示最终评估结果
    print("\n" + "="*60)
    print("🎯 最终评估结果")
    print("="*60)
    print(f"📊 数据集: {current_dataset['name']}")
    print(f"📝 处理样本数: {len(test_data)}")
    print(f"🎯 真实实体总数: {total_entities}")
    print(f"🔍 预测实体总数: {predicted_entities}")
    print(f"✅ 正确预测数: {correct_predictions}")
    print("-" * 40)
    print(f"📈 Precision: {precision:.4f} ({correct_predictions}/{predicted_entities})")
    print(f"📈 Recall: {recall:.4f} ({correct_predictions}/{total_entities})")
    print(f"📈 F1-Score: {f1_score:.4f}")
    print("="*60)
    
    # 保存评估结果
    eval_results = {
        'dataset': current_dataset['name'],
        'timestamp': datetime.now().isoformat(),
        'samples_count': len(test_data),
        'total_entities': total_entities,
        'predicted_entities': predicted_entities,
        'correct_predictions': correct_predictions,
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score,
        'processing_mode': 'unified_three_stage',
        'detailed_results': results
    }
    
    # 保存到文件
    results_dir = "results"
    os.makedirs(results_dir, exist_ok=True)
    eval_file = os.path.join(results_dir, f"eval_results_unified_{current_dataset['name'].lower().replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    try:
        with open(eval_file, 'w', encoding='utf-8') as f:
            json.dump(eval_results, f, ensure_ascii=False, indent=2)
        print(f"💾 详细结果已保存到: {eval_file}")
    except Exception as e:
        print(f"⚠️ 保存结果失败: {e}")
    
    return eval_results


async def main():
    """主函数 - 三阶段统一处理"""
    parser = argparse.ArgumentParser(description='🧠 APIICL - 元认知智能体NER系统 (三阶段统一处理)')
    parser.add_argument('--dataset', '-d', type=str, default='ace2005', 
                       help='数据集名称 (默认: ace2005)')
    parser.add_argument('--max-samples', type=int, 
                       help='最大测试样本数 (默认: 处理全部)')
    parser.add_argument('--log-level', type=str, default='WARNING', 
                       help='日志级别 (DEBUG/INFO/WARNING/ERROR)')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)

    # 初始化数据集配置
    initialize_datasets()

    # 打印横幅
    print_banner()
    
    # 设置数据集
    if not set_dataset(args.dataset):
        print(f"❌ 数据集不存在: {args.dataset}")
        available = list_available_datasets()
        print("\n可用数据集:")
        for key, info in available.items():
            status = "✅" if info['available'] else "❌"
            current = "👈 当前" if info['current'] else ""
            print(f"  {status} {key}: {info['name']} {current}")
        return
    
    # 显示当前配置
    current_dataset = get_current_dataset_info()
    print(f"📊 数据集: {current_dataset['name']}")
    if args.max_samples:
        print(f"📝 最大样本数: {args.max_samples}")
    print()
    
    try:
        # 执行三阶段统一处理
        await process_and_eval_dataset(args.max_samples)
        
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
